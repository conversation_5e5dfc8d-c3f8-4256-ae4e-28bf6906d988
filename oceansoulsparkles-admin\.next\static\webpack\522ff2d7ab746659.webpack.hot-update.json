{"c": ["webpack"], "r": ["pages/admin/login", "pages/admin/dashboard", "/_error", "pages/admin/pos", "lib_pos-auth-protection_js"], "m": ["./components/auth/LoginForm.tsx", "./components/auth/MFAForm.tsx", "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./styles/admin/Login.module.css", "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./styles/admin/LoginForm.module.css", "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./styles/admin/MFAForm.module.css", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cconne%5CDownloads%5Cconnectplusapps%20website%5Cadmin.oceansoulsparkles.com.au%5Coceansoulsparkles-admin%5Cpages%5Cadmin%5Clogin.tsx&page=%2Fadmin%2Flogin!", "./node_modules/react-hook-form/dist/index.esm.mjs", "./pages/admin/login.tsx", "./styles/admin/Login.module.css", "./styles/admin/LoginForm.module.css", "./styles/admin/MFAForm.module.css", "./components/admin/ActivityFeed.tsx", "./components/admin/DashboardStats.tsx", "./components/admin/QuickActions.tsx", "./components/admin/RecentBookings.tsx", "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./styles/admin/ActivityFeed.module.css", "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./styles/admin/Dashboard.module.css", "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./styles/admin/DashboardStats.module.css", "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./styles/admin/QuickActions.module.css", "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./styles/admin/RecentBookings.module.css", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cconne%5CDownloads%5Cconnectplusapps%20website%5Cadmin.oceansoulsparkles.com.au%5Coceansoulsparkles-admin%5Cpages%5Cadmin%5Cdashboard.tsx&page=%2Fadmin%2Fdashboard!", "./pages/admin/dashboard.tsx", "./styles/admin/ActivityFeed.module.css", "./styles/admin/Dashboard.module.css", "./styles/admin/DashboardStats.module.css", "./styles/admin/QuickActions.module.css", "./styles/admin/RecentBookings.module.css", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cconne%5CDownloads%5Cconnectplusapps%20website%5Cadmin.oceansoulsparkles.com.au%5Coceansoulsparkles-admin%5Cnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&page=%2F_error!", "./components/admin/PullToRefresh.tsx", "./components/admin/mobile/ActionSheet.tsx", "./components/admin/mobile/MobileModal.tsx", "./components/admin/mobile/MobilePOS.tsx", "./components/admin/pos/POSCheckout.js", "./components/admin/pos/POSSquarePayment.js", "./components/admin/pos/POSSquareTerminal.js", "./components/admin/pos/PaymentMethodSelector.js", "./components/admin/pos/ServiceBookingAvailability.js", "./components/admin/pos/TipManagement.js", "./lib/receipt-generator.js", "./lib/safe-render-utils.js", "./lib/supabase.js", "./node_modules/@supabase/auth-js/dist/module/AuthAdminApi.js", "./node_modules/@supabase/auth-js/dist/module/AuthClient.js", "./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js", "./node_modules/@supabase/auth-js/dist/module/GoTrueClient.js", "./node_modules/@supabase/auth-js/dist/module/index.js", "./node_modules/@supabase/auth-js/dist/module/lib/base64url.js", "./node_modules/@supabase/auth-js/dist/module/lib/constants.js", "./node_modules/@supabase/auth-js/dist/module/lib/errors.js", "./node_modules/@supabase/auth-js/dist/module/lib/fetch.js", "./node_modules/@supabase/auth-js/dist/module/lib/helpers.js", "./node_modules/@supabase/auth-js/dist/module/lib/local-storage.js", "./node_modules/@supabase/auth-js/dist/module/lib/locks.js", "./node_modules/@supabase/auth-js/dist/module/lib/polyfills.js", "./node_modules/@supabase/auth-js/dist/module/lib/types.js", "./node_modules/@supabase/auth-js/dist/module/lib/version.js", "./node_modules/@supabase/functions-js/dist/module/FunctionsClient.js", "./node_modules/@supabase/functions-js/dist/module/helper.js", "./node_modules/@supabase/functions-js/dist/module/index.js", "./node_modules/@supabase/functions-js/dist/module/types.js", "./node_modules/@supabase/node-fetch/browser.js", "./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js", "./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestClient.js", "./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js", "./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js", "./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js", "./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js", "./node_modules/@supabase/postgrest-js/dist/cjs/constants.js", "./node_modules/@supabase/postgrest-js/dist/cjs/index.js", "./node_modules/@supabase/postgrest-js/dist/cjs/version.js", "./node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs", "./node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js", "./node_modules/@supabase/realtime-js/dist/module/RealtimeClient.js", "./node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js", "./node_modules/@supabase/realtime-js/dist/module/WebSocket.js", "./node_modules/@supabase/realtime-js/dist/module/index.js", "./node_modules/@supabase/realtime-js/dist/module/lib/constants.js", "./node_modules/@supabase/realtime-js/dist/module/lib/push.js", "./node_modules/@supabase/realtime-js/dist/module/lib/serializer.js", "./node_modules/@supabase/realtime-js/dist/module/lib/timer.js", "./node_modules/@supabase/realtime-js/dist/module/lib/transformers.js", "./node_modules/@supabase/realtime-js/dist/module/lib/version.js", "./node_modules/@supabase/storage-js/dist/module/StorageClient.js", "./node_modules/@supabase/storage-js/dist/module/index.js", "./node_modules/@supabase/storage-js/dist/module/lib/constants.js", "./node_modules/@supabase/storage-js/dist/module/lib/errors.js", "./node_modules/@supabase/storage-js/dist/module/lib/fetch.js", "./node_modules/@supabase/storage-js/dist/module/lib/helpers.js", "./node_modules/@supabase/storage-js/dist/module/lib/types.js", "./node_modules/@supabase/storage-js/dist/module/lib/version.js", "./node_modules/@supabase/storage-js/dist/module/packages/StorageBucketApi.js", "./node_modules/@supabase/storage-js/dist/module/packages/StorageFileApi.js", "./node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js", "./node_modules/@supabase/supabase-js/dist/module/index.js", "./node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js", "./node_modules/@supabase/supabase-js/dist/module/lib/constants.js", "./node_modules/@supabase/supabase-js/dist/module/lib/fetch.js", "./node_modules/@supabase/supabase-js/dist/module/lib/helpers.js", "./node_modules/@supabase/supabase-js/dist/module/lib/version.js", "./node_modules/base64-js/index.js", "./node_modules/buffer/index.js", "./node_modules/ieee754/index.js", "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./styles/admin/POS.module.css", "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./styles/admin/mobile/ActionSheet.module.css", "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./styles/admin/mobile/MobileModal.module.css", "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./styles/admin/mobile/MobilePOS.module.css", "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./styles/admin/mobile/PullToRefresh.module.css", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cconne%5CDownloads%5Cconnectplusapps%20website%5Cadmin.oceansoulsparkles.com.au%5Coceansoulsparkles-admin%5Cpages%5Cadmin%5Cpos.js&page=%2Fadmin%2Fpos!", "./node_modules/ws/browser.js", "./pages/admin/pos.js", "./styles/admin/POS.module.css", "./styles/admin/mobile/ActionSheet.module.css", "./styles/admin/mobile/MobileModal.module.css", "./styles/admin/mobile/MobilePOS.module.css", "./styles/admin/mobile/PullToRefresh.module.css", "./lib/pos-auth-protection.js"]}